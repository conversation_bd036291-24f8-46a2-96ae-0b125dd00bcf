---
import GoogleAnalytics from '@astro-kits/google-analytics';
import { ClientRouter } from "astro:transitions";
import LanguageSwitcher from '../components/LanguageSwitcher.astro';
import { getLocaleFromUrl, useTranslations } from '../i18n/utils';
import { fileURLToPath } from 'url';
import { dirname, relative } from 'path';

// Get the current file path relative to src folder
const currentFilePath = fileURLToPath(import.meta.url);
const srcPath = fileURLToPath(new URL('../src', import.meta.url));
const relativeFilePath = relative(srcPath, currentFilePath);

console.log('File path relative to src:', relativeFilePath); // This will log: layouts/Layout.astro

const currentLocale = getLocaleFromUrl(Astro.url);
const t = useTranslations(currentLocale);

export interface Props {
  title?: string;
  description?: string;
}

const { title, description } = Astro.props;
---
<!doctype html>
<html lang={currentLocale}>
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title || t('Layout.title')}</title>
    <meta name="description" content={description || t('Layout.description')} />
    <ClientRouter />
    <GoogleAnalytics />
	</head>
	<body>
		<LanguageSwitcher />
		<slot />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
