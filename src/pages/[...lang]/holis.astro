---
import Layout from '../../layouts/Layout.astro';
import { useTranslations, getLocalizedPath, type Locale } from "../../i18n/utils";

export async function getStaticPaths() {
  return [
    { params: { lang: undefined } }, // English (no prefix) - generates /holis
    { params: { lang: 'es' } }       // Spanish - generates /es/holis
  ];
}

const { lang } = Astro.params;
const currentLocale: Locale = (lang as Locale) || 'en';
const t = useTranslations(currentLocale);
console.log(Astro.url);
---

<Layout>
  <div style="padding: 2rem; text-align: center;">
    <h1>{t('hello.title')}</h1>
    <a href={getLocalizedPath('/', currentLocale)} style="display: inline-block; margin-top: 1rem; padding: 8px 16px; background: #3245ff; color: white; text-decoration: none; border-radius: 6px;">
      {t('hello.go_to_home')}
    </a>
  </div>
</Layout>
