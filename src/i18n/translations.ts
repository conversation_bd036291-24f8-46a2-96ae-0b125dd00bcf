export const translations = {
  en: {
    // Layout
    Layout: {
      title: 'bookea.link',
      description: 'Booking for all types of businesses',
    },
    
    // Navigation
    'nav.home': 'Home',
    'nav.hello': 'Hello',
    
    // Index page
    index: {
      'go_to_hello': 'Go to hello',
      'go_to_holis': 'Go to holis',
    },
    
    // Hello page
    'hello.title': 'Hello',
    'hello.go_to_home': 'Go to Home',
    
    // Welcome component
    'welcome.get_started': 'To get started, open the',
    'welcome.directory': 'directory in your project.',
    'welcome.read_docs': 'Read our docs',
    'welcome.join_discord': 'Join our Discord',
    'welcome.whats_new_title': "What's New in Astro 5.0?",
    'welcome.whats_new_description': 'From content layers to server islands, click to learn more about the new features and improvements in Astro 5.0',
  },
  es: {
    // Layout
    Layout: {
      title: 'bookea.link',
      description: 'Reservas para todo tipo de negocios',
    },

    // Navigation
    'nav.home': 'Inicio',
    'nav.hello': 'Hola',
    
    // Index page
    index: {
      'go_to_hello': 'Ir a hola',
      'go_to_holis': 'Ir a holis',
    },
    
    // Hello page
    'hello.title': 'Hola',
    'hello.go_to_home': 'Ir al Inicio',
    
    // Welcome component
    'welcome.get_started': 'Para comenzar, abre el directorio',
    'welcome.directory': 'en tu proyecto.',
    'welcome.read_docs': 'Lee nuestra documentación',
    'welcome.join_discord': 'Únete a nuestro Discord',
    'welcome.whats_new_title': '¿Qué hay de nuevo en Astro 5.0?',
    'welcome.whats_new_description': 'Desde capas de contenido hasta islas de servidor, haz clic para conocer más sobre las nuevas características y mejoras en Astro 5.0',
  }
} as const;

export type TranslationKey = keyof typeof translations.en;
